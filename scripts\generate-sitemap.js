#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  baseUrl: 'https://lunarcubes.com.np',
  outputPath: './public/sitemap.xml',
  appDir: './src/app',
  dataDir: './public/data',
  defaultChangeFreq: 'weekly',
  defaultPriority: '0.8'
};

// Route priorities and change frequencies
const routeConfig = {
  '/': { priority: '1.0', changefreq: 'weekly' },
  '/about': { priority: '0.8', changefreq: 'monthly' },
  '/services': { priority: '0.9', changefreq: 'weekly' },
  '/portfolio': { priority: '0.8', changefreq: 'weekly' },
  '/contact': { priority: '0.7', changefreq: 'monthly' },
  '/careers': { priority: '0.8', changefreq: 'weekly' },
  '/blog': { priority: '0.9', changefreq: 'daily' },
  '/blog/[slug]': { priority: '0.7', changefreq: 'weekly' },
  '/portfolio/[id]': { priority: '0.6', changefreq: 'monthly' }
};

/**
 * Get all static routes from the app directory
 */
function getStaticRoutes(dir, basePath = '') {
  const routes = [];
  
  try {
    const items = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const item of items) {
      if (item.isDirectory()) {
        // Skip dynamic route directories for now
        if (item.name.startsWith('[') && item.name.endsWith(']')) {
          continue;
        }
        
        const routePath = basePath + '/' + item.name;
        const fullPath = path.join(dir, item.name);
        
        // Check if this directory has a page.tsx file
        const pageFile = path.join(fullPath, 'page.tsx');
        if (fs.existsSync(pageFile)) {
          routes.push(routePath);
        }
        
        // Recursively get routes from subdirectories
        routes.push(...getStaticRoutes(fullPath, routePath));
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
  }
  
  return routes;
}

/**
 * Get dynamic routes from data files
 */
function getDynamicRoutes() {
  const routes = [];

  try {
    // Get blog post routes
    const blogPostsPath = path.join(config.dataDir, 'blog-posts.json');
    if (fs.existsSync(blogPostsPath)) {
      const blogPosts = JSON.parse(fs.readFileSync(blogPostsPath, 'utf8'));
      blogPosts.forEach(post => {
        if (post.status === 'published' && post.slug) {
          routes.push({
            path: `/blog/${post.slug}`,
            lastmod: post.updatedAt || post.publishedAt,
            priority: routeConfig['/blog/[slug]']?.priority || '0.7',
            changefreq: routeConfig['/blog/[slug]']?.changefreq || 'weekly'
          });
        }
      });
    }

    // Get portfolio routes
    const portfolioPath = path.join(config.dataDir, 'portfolio.json');
    if (fs.existsSync(portfolioPath)) {
      const portfolioItems = JSON.parse(fs.readFileSync(portfolioPath, 'utf8'));
      portfolioItems.forEach(item => {
        if (item.id) {
          routes.push({
            path: `/portfolio/${item.id}`,
            lastmod: new Date().toISOString(),
            priority: routeConfig['/portfolio/[id]']?.priority || '0.6',
            changefreq: routeConfig['/portfolio/[id]']?.changefreq || 'monthly'
          });
        }
      });
    }

    // Get job posting routes
    const jobsPath = path.join(config.dataDir, 'jobs.json');
    if (fs.existsSync(jobsPath)) {
      const jobPostings = JSON.parse(fs.readFileSync(jobsPath, 'utf8'));
      jobPostings.forEach(job => {
        if (job.status === 'active' && job.slug) {
          routes.push({
            path: `/careers/${job.slug}`,
            lastmod: job.updatedAt || job.publishedAt,
            priority: '0.6',
            changefreq: 'weekly'
          });
        }
      });
    }

  } catch (error) {
    console.error('Error reading dynamic routes:', error.message);
  }

  return routes;
}

/**
 * Generate XML sitemap content
 */
function generateSitemapXML(routes) {
  const currentDate = new Date().toISOString().split('T')[0];
  
  let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
  xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n';
  
  routes.forEach(route => {
    xml += '  <url>\n';
    xml += `    <loc>${config.baseUrl}${route.path}</loc>\n`;
    xml += `    <lastmod>${route.lastmod || currentDate}</lastmod>\n`;
    xml += `    <changefreq>${route.changefreq}</changefreq>\n`;
    xml += `    <priority>${route.priority}</priority>\n`;
    xml += '  </url>\n';
  });
  
  xml += '</urlset>\n';
  return xml;
}

/**
 * Generate robots.txt file
 */
function generateRobotsTxt() {
  const robotsContent = `User-agent: *
Allow: /

# Sitemap
Sitemap: ${config.baseUrl}/sitemap.xml

# Disallow admin and private areas
Disallow: /admin/
Disallow: /api/
Disallow: /_next/
Disallow: /private/

# Allow common crawlers
User-agent: Googlebot
Allow: /

User-agent: Bingbot
Allow: /

# Crawl delay
Crawl-delay: 1
`;

  const robotsPath = './public/robots.txt';
  fs.writeFileSync(robotsPath, robotsContent, 'utf8');
  console.log(`🤖 robots.txt updated at ${robotsPath}`);
}

/**
 * Validate URLs before adding to sitemap
 */
function validateUrl(url) {
  // Basic URL validation
  if (!url || typeof url !== 'string') return false;
  if (url.includes('..') || url.includes('//')) return false;
  if (url.length > 2048) return false; // URL too long
  return true;
}

/**
 * Main function to generate sitemap
 */
function generateSitemap() {
  console.log('🚀 Generating sitemap...');

  try {
    // Get static routes
    const staticRoutes = getStaticRoutes(config.appDir);

    // Add root route if not present
    if (!staticRoutes.includes('')) {
      staticRoutes.unshift('');
    }

    // Add careers route if not present (in case the page doesn't exist yet)
    if (!staticRoutes.includes('/careers')) {
      staticRoutes.push('/careers');
    }

    // Convert static routes to route objects
    const staticRouteObjects = staticRoutes.map(route => {
      const routePath = route || '/';
      const routeConfigItem = routeConfig[routePath] || {};

      return {
        path: routePath,
        priority: routeConfigItem.priority || config.defaultPriority,
        changefreq: routeConfigItem.changefreq || config.defaultChangeFreq,
        lastmod: new Date().toISOString().split('T')[0]
      };
    });

    // Get dynamic routes
    const dynamicRoutes = getDynamicRoutes();

    // Combine all routes and validate
    const allRoutes = [...staticRouteObjects, ...dynamicRoutes].filter(route =>
      validateUrl(route.path)
    );

    // Sort routes by priority (descending) and then alphabetically
    allRoutes.sort((a, b) => {
      const priorityDiff = parseFloat(b.priority) - parseFloat(a.priority);
      if (priorityDiff !== 0) return priorityDiff;
      return a.path.localeCompare(b.path);
    });

    // Generate XML
    const sitemapXML = generateSitemapXML(allRoutes);

    // Ensure output directory exists
    const outputDir = path.dirname(config.outputPath);
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Write sitemap to file
    fs.writeFileSync(config.outputPath, sitemapXML, 'utf8');

    // Generate robots.txt
    generateRobotsTxt();

    console.log(`✅ Sitemap generated successfully!`);
    console.log(`📍 Location: ${config.outputPath}`);
    console.log(`📊 Total URLs: ${allRoutes.length}`);
    console.log(`   - Static routes: ${staticRouteObjects.length}`);
    console.log(`   - Dynamic routes: ${dynamicRoutes.length}`);

    // Display routes for verification
    console.log('\n📋 Generated routes:');
    allRoutes.forEach(route => {
      console.log(`   ${route.path} (priority: ${route.priority})`);
    });

  } catch (error) {
    console.error('❌ Error generating sitemap:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  generateSitemap();
}

module.exports = { generateSitemap };
