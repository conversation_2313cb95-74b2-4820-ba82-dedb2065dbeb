#!/usr/bin/env tsx

import { generateAndSaveSitemap, saveRobotsTxt } from '../src/lib/sitemap-generator';

/**
 * CLI script to generate sitemap and robots.txt
 */
async function main() {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://lunarcubes.com.np';
  
  console.log('🚀 Generating sitemap and robots.txt...');
  console.log(`🌐 Base URL: ${baseUrl}`);
  
  try {
    // Generate sitemap
    generateAndSaveSitemap(baseUrl);
    
    // Generate robots.txt
    saveRobotsTxt(baseUrl);
    
    console.log('✅ All files generated successfully!');
    
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}
