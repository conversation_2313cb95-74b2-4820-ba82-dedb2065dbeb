'use client';

import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  MapPin, 
  Phone, 
  Mail, 
  Facebook, 
  Instagram, 
  Linkedin, 
  Twitter,
  ArrowRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useCompanyInfo } from '@/lib/queries/hooks';

const services = [
  { name: 'Social Media Marketing', href: '/services#social-media-marketing' },
  { name: 'SEO Optimization', href: '/services#search-engine-optimization' },
  { name: 'Web Development', href: '/services#web-development' },
  { name: 'Google Ads', href: '/services#google-ads' },
  { name: 'Content Marketing', href: '/services#content-marketing' },
  { name: 'Brand Identity', href: '/services#brand-identity' },
];

const quickLinks = [
  { name: 'About Us', href: '/about' },
  { name: 'Our Team', href: '/about#team' },
  { name: 'Portfolio', href: '/portfolio' },
  { name: 'Case Studies', href: '/case-studies' },
  { name: 'Blog', href: '/blog' },
  { name: 'Careers', href: '/careers' },
  { name: 'Contact', href: '/contact' },
];

const industries = [
  { name: 'Hospitality & Tourism', href: '/industries#hospitality-tourism' },
  { name: 'Retail & E-commerce', href: '/industries#retail-ecommerce' },
  { name: 'Healthcare & Wellness', href: '/industries#healthcare-wellness' },
  { name: 'Technology & Startups', href: '/industries#technology-startups' },
  { name: 'Food & Beverage', href: '/industries#food-beverage' },
  { name: 'Professional Services', href: '/industries#professional-services' },
];

export default function Footer() {
  const { data: company } = useCompanyInfo();

  const handleNewsletterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Newsletter subscription logic will be implemented
  };

  return (
    <footer className="bg-gray-900 dark:bg-gray-950 text-white">
      {/* Newsletter Section */}
      <div className="bg-brand-navy dark:bg-blue-900 py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
            >
              <h3 className="text-2xl md:text-3xl font-bold mb-4">
                Stay Updated with Digital Marketing Insights
              </h3>
              <p className="text-blue-100 dark:text-blue-200 mb-8 max-w-2xl mx-auto">
                Get the latest digital marketing tips, industry insights, and exclusive offers 
                delivered to your inbox. Join 500+ Nepali business owners who trust our expertise.
              </p>
              <form onSubmit={handleNewsletterSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <Input
                  type="email"
                  placeholder="Enter your email address"
                  className="flex-1 bg-white text-gray-900"
                  required
                />
                <Button type="submit" className="bg-brand-gold hover:bg-brand-gold-dark dark:bg-yellow-500 dark:hover:bg-yellow-600 text-gray-900 font-semibold">
                  Subscribe
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </form>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="lg:col-span-1">
              <div className="flex items-center space-x-2 mb-6">
                <div className="h-10 w-10 bg-gradient-to-br from-brand-navy to-brand-gold rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">LC</span>
                </div>
                <div>
                  <h3 className="text-xl font-bold">Lunar Cubes</h3>
                  <p className="text-sm text-gray-400 dark:text-gray-500">From Concept to Cosmos</p>
                </div>
              </div>
              <p className="text-gray-300 dark:text-gray-400 mb-6 leading-relaxed">
                Nepal&apos;s premier digital marketing agency helping SMEs reach new heights 
                in the digital landscape with innovative strategies and local market expertise.
              </p>
              
              {/* Contact Info */}
              {company && (
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <MapPin className="h-5 w-5 text-brand-gold dark:text-yellow-400 flex-shrink-0" />
                    <span className="text-gray-300 dark:text-gray-400">{company.location.address}, {company.location.city}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-brand-gold dark:text-yellow-400 flex-shrink-0" />
                    <span className="text-gray-300 dark:text-gray-400">{company.contact.phone}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-brand-gold dark:text-yellow-400 flex-shrink-0" />
                    <span className="text-gray-300 dark:text-gray-400">{company.contact.email}</span>
                  </div>
                </div>
              )}
            </div>

            {/* Services */}
            <div>
              <h4 className="text-lg font-semibold mb-6">Our Services</h4>
              <ul className="space-y-3">
                {services.map((service) => (
                  <li key={service.name}>
                    <Link 
                      href={service.href}
                      className="text-gray-300 hover:text-brand-gold transition-colors duration-200"
                    >
                      {service.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-lg font-semibold mb-6">Quick Links</h4>
              <ul className="space-y-3">
                {quickLinks.map((link) => (
                  <li key={link.name}>
                    <Link 
                      href={link.href}
                      className="text-gray-300 hover:text-brand-gold transition-colors duration-200"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Industries */}
            <div>
              <h4 className="text-lg font-semibold mb-6">Industries We Serve</h4>
              <ul className="space-y-3">
                {industries.map((industry) => (
                  <li key={industry.name}>
                    <Link 
                      href={industry.href}
                      className="text-gray-300 hover:text-brand-gold transition-colors duration-200"
                    >
                      {industry.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-800 py-6">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm">
              © {new Date().getFullYear()} Lunar Cubes. All rights reserved. | 
              <Link href="/privacy" className="hover:text-brand-gold ml-1">Privacy Policy</Link> | 
              <Link href="/terms" className="hover:text-brand-gold ml-1">Terms of Service</Link>
            </div>
            
            {/* Social Links */}
            {company?.social && (
              <div className="flex space-x-4">
                {company.social.facebook && (
                  <a 
                    href={company.social.facebook}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-400 hover:text-brand-gold transition-colors duration-200"
                    aria-label="Facebook"
                  >
                    <Facebook className="h-5 w-5" />
                  </a>
                )}
                {company.social.instagram && (
                  <a 
                    href={company.social.instagram}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-400 hover:text-brand-gold transition-colors duration-200"
                    aria-label="Instagram"
                  >
                    <Instagram className="h-5 w-5" />
                  </a>
                )}
                {company.social.linkedin && (
                  <a 
                    href={company.social.linkedin}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-400 hover:text-brand-gold transition-colors duration-200"
                    aria-label="LinkedIn"
                  >
                    <Linkedin className="h-5 w-5" />
                  </a>
                )}
                {company.social.twitter && (
                  <a 
                    href={company.social.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-400 hover:text-brand-gold transition-colors duration-200"
                    aria-label="Twitter"
                  >
                    <Twitter className="h-5 w-5" />
                  </a>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </footer>
  );
}
