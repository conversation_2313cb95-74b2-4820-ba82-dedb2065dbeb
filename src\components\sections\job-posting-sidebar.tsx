'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { 
  MapPin, 
  Clock, 
  Users, 
  DollarSign, 
  Calendar,
  Share2,
  Bookmark,
  ArrowRight,
  Mail,
  Phone
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface JobPosting {
  id: string;
  slug: string;
  title: string;
  department: string;
  location: string;
  type: 'full-time' | 'part-time' | 'contract' | 'internship';
  status: 'active' | 'closed' | 'draft';
  publishedAt: string;
  updatedAt?: string;
  description: string;
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
  salary?: {
    min: number;
    max: number;
    currency: string;
    period: string;
  };
  duration?: string;
}

interface JobPostingSidebarProps {
  job: JobPosting;
}

const jobTypeColors = {
  'full-time': 'bg-green-100 text-green-800',
  'part-time': 'bg-blue-100 text-blue-800',
  'contract': 'bg-purple-100 text-purple-800',
  'internship': 'bg-orange-100 text-orange-800',
};

export default function JobPostingSidebar({ job }: JobPostingSidebarProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `${job.title} - Lunar Cubes Careers`,
          text: `Check out this job opportunity: ${job.title} at Lunar Cubes`,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      // You could show a toast notification here
    }
  };

  return (
    <div className="space-y-6">
      {/* Quick Apply Card */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true }}
        className="bg-gradient-to-br from-brand-navy to-brand-navy-dark rounded-2xl p-6 text-white sticky top-6"
      >
        <h3 className="text-xl font-bold mb-4">Ready to Apply?</h3>
        <p className="text-gray-300 mb-6 text-sm">
          Join our team and be part of something amazing. Apply now and let's build the future together.
        </p>
        <Button 
          size="lg" 
          className="w-full bg-brand-gold hover:bg-yellow-500 text-brand-navy font-semibold mb-4"
          onClick={() => {
            const applicationSection = document.getElementById('application-form');
            applicationSection?.scrollIntoView({ behavior: 'smooth' });
          }}
        >
          Apply Now
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="flex-1 border-white/20 text-white hover:bg-white hover:text-brand-navy"
            onClick={handleShare}
          >
            <Share2 className="h-4 w-4 mr-2" />
            Share
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="flex-1 border-white/20 text-white hover:bg-white hover:text-brand-navy"
          >
            <Bookmark className="h-4 w-4 mr-2" />
            Save
          </Button>
        </div>
      </motion.div>

      {/* Job Details Card */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.1 }}
        viewport={{ once: true }}
        className="bg-white rounded-2xl p-6 shadow-lg"
      >
        <h3 className="text-lg font-bold text-gray-900 mb-4">Job Details</h3>
        
        <div className="space-y-4">
          <div className="flex items-center gap-3">
            <MapPin className="h-5 w-5 text-brand-navy" />
            <div>
              <p className="text-sm text-gray-500">Location</p>
              <p className="font-medium text-gray-900">{job.location}</p>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Clock className="h-5 w-5 text-brand-navy" />
            <div>
              <p className="text-sm text-gray-500">Employment Type</p>
              <Badge className={`${jobTypeColors[job.type]} border-0 font-medium`}>
                {job.type.charAt(0).toUpperCase() + job.type.slice(1).replace('-', ' ')}
              </Badge>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <Users className="h-5 w-5 text-brand-navy" />
            <div>
              <p className="text-sm text-gray-500">Department</p>
              <p className="font-medium text-gray-900">{job.department}</p>
            </div>
          </div>

          {job.salary && (
            <div className="flex items-center gap-3">
              <DollarSign className="h-5 w-5 text-brand-navy" />
              <div>
                <p className="text-sm text-gray-500">Salary Range</p>
                <p className="font-medium text-gray-900">
                  {job.salary.currency} {job.salary.min.toLocaleString()} - {job.salary.max.toLocaleString()}
                  <span className="text-sm text-gray-500">/{job.salary.period}</span>
                </p>
              </div>
            </div>
          )}

          {job.duration && (
            <div className="flex items-center gap-3">
              <Calendar className="h-5 w-5 text-brand-navy" />
              <div>
                <p className="text-sm text-gray-500">Duration</p>
                <p className="font-medium text-gray-900">{job.duration}</p>
              </div>
            </div>
          )}

          <div className="flex items-center gap-3">
            <Calendar className="h-5 w-5 text-brand-navy" />
            <div>
              <p className="text-sm text-gray-500">Posted</p>
              <p className="font-medium text-gray-900">{formatDate(job.publishedAt)}</p>
            </div>
          </div>
        </div>
      </motion.div>

      {/* Contact HR Card */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        viewport={{ once: true }}
        className="bg-white rounded-2xl p-6 shadow-lg"
      >
        <h3 className="text-lg font-bold text-gray-900 mb-4">Have Questions?</h3>
        <p className="text-gray-600 text-sm mb-4">
          Get in touch with our HR team for more information about this position.
        </p>
        
        <div className="space-y-3">
          <a 
            href="mailto:<EMAIL>"
            className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Mail className="h-5 w-5 text-brand-navy" />
            <div>
              <p className="font-medium text-gray-900">Email HR</p>
              <p className="text-sm text-gray-500"><EMAIL></p>
            </div>
          </a>
          
          <a 
            href="tel:+9779851234567"
            className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Phone className="h-5 w-5 text-brand-navy" />
            <div>
              <p className="font-medium text-gray-900">Call HR</p>
              <p className="text-sm text-gray-500">+977 ************</p>
            </div>
          </a>
        </div>
      </motion.div>

      {/* Other Opportunities */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
        viewport={{ once: true }}
        className="bg-white rounded-2xl p-6 shadow-lg"
      >
        <h3 className="text-lg font-bold text-gray-900 mb-4">Other Opportunities</h3>
        <p className="text-gray-600 text-sm mb-4">
          Explore other exciting career opportunities at Lunar Cubes.
        </p>
        <Button 
          asChild 
          variant="outline" 
          className="w-full border-brand-navy text-brand-navy hover:bg-brand-navy hover:text-white"
        >
          <Link href="/careers">
            View All Positions
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </motion.div>
    </div>
  );
}
